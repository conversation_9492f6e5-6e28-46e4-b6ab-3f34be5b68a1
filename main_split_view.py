from maix import image, display, app, time, camera, touchscreen
import cv2
import numpy as np
import math
import gc
from micu_uart_lib import (
    SimpleUART, micu_printf, bind_variable,
    VariableContainer, clear_variable_bindings
)

# --------------------------- 配置参数区（所有过滤条件集中在这里） ---------------------------
# 矩形检测核心参数
MIN_CONTOUR_AREA = 500       # 最小轮廓面积（过滤小目标）
MAX_CONTOUR_AREA = 60000     # 最大轮廓面积（过滤大目标）
TARGET_SIDES = 4             # 目标边数（矩形为4）
BINARY_THRESHOLD = 66        # 二值化阈值

# 宽高比过滤参数（宽/高）
MIN_ASPECT_RATIO = 0.6       # 最小宽高比（高不超过宽的1.67倍）/0.6
MAX_ASPECT_RATIO = 1.7       # 最大宽高比（宽不超过高的1.7倍）/1.7

# 角度过滤参数（°）
MIN_ANGLE = 75               # 最小直角角度（接近90°）
MAX_ANGLE = 105               # 最大直角角度（接近90°）

# 对边长度一致性参数（比例）
MIN_OPPOSITE_RATIO = 0.7     # 最小对边比例（允许±20%偏差）/0.7
MAX_OPPOSITE_RATIO = 1.3     # 最大对边比例/1.3

# 透视变换与圆形参数
CORRECTED_WIDTH = 200        # 校正后矩形宽度
CORRECTED_HEIGHT = 150       # 校正后矩形高度
CIRCLE_RADIUS = 50           # 圆形轨迹半径
CIRCLE_NUM_POINTS = 12       # 圆形轨迹点数量

# 触摸按键参数
TOUCH_DEBOUNCE = 0.3         # 触摸防抖动时间（秒）

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)
        
    def detect(self, img):
        return img, []  # 暂时返回空的激光点列表

# --------------------------- 虚拟按键类 ---------------------------
class VirtualButtons:
    def __init__(self):
        # 按键定义 [x, y, width, height, text, action] - 优化布局到黑边区域
        self.buttons = [
            [50, 250, 50, 25, "Center", "center"],    # 中心点模式 - 下方黑边区域
            [120, 250, 50, 25, "Circle", "circle"],   # 圆形模式 - 下方黑边区域
            [200, 250, 30, 25, "T-", "thresh_down"],  # 阈值减少 - 下方黑边区域
            [250, 250, 30, 25, "T+", "thresh_up"]     # 阈值增加 - 下方黑边区域
        ]

        # 触摸检测区域 - 与按键显示位置完全匹配
        self.touch_areas = [
            [50, 250, 50, 25],   # Center按键触摸区域
            [120, 250, 50, 25],  # Circle按键触摸区域
            [200, 250, 30, 25],  # T-按键触摸区域
            [250, 250, 30, 25]   # T+按键触摸区域
        ]

        self.last_touch_time = 0
        self.touch_debounce = 0.3

    def check_touch(self, touch_x, touch_y):
        current_time = time.time()
        if current_time - self.last_touch_time < self.touch_debounce:
            return None

        for i, touch_area in enumerate(self.touch_areas):
            area_x, area_y, area_w, area_h = touch_area
            if area_x <= touch_x <= area_x + area_w and area_y <= touch_y <= area_y + area_h:
                self.last_touch_time = current_time
                return self.buttons[i][5]
        return None

    def draw_buttons(self, img, current_mode, threshold=BINARY_THRESHOLD):
        for button in self.buttons:
            x, y, w, h, text, action = button
            # 按键高亮逻辑
            if (action == "center" and current_mode == "center") or (action == "circle" and current_mode == "circle"):
                color = (0, 255, 255)  # 黄色高亮
                thickness = 3
            elif action in ["thresh_up", "thresh_down"]:
                color = (0, 255, 0)    # 绿色
                thickness = 2
            else:
                color = (255, 255, 255)  # 白色
                thickness = 2
            
            # 绘制按键边框
            cv2.rectangle(img, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制按键文字 - 优化文字居中和大小
            text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.4, 1)[0]
            text_x = x + (w - text_size[0]) // 2
            text_y = y + (h + text_size[1]) // 2
            cv2.putText(img, text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        
        # 阈值显示位置调整到按键附近
        cv2.putText(img, f"Threshold: {threshold}", (300, 250), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

# 触摸屏初始化
def init_touchscreen():
    try:
        ts = touchscreen.TouchScreen()
        print("TouchScreen initialized successfully")
        return ts
    except Exception as e:
        print(f"TouchScreen init failed: {e}")
        return None

# --------------------------- 工具函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

def perspective_transform(pts, target_width, target_height):
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    dst_pts = np.array([  
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)
    return M, M_inv, src_pts

def is_regular_rectangle(approx):
    """使用配置参数判断是否为规则矩形，返回详细信息"""
    if not cv2.isContourConvex(approx):
        return False, "非凸多边形", None
    
    pts = approx.reshape(4, 2).astype(np.float32)
    p0, p1, p2, p3 = pts[0], pts[1], pts[2], pts[3]
    
    # 计算四条边的长度
    edge_lengths = [
        math.hypot(p1[0]-p0[0], p1[1]-p0[1]),
        math.hypot(p2[0]-p1[0], p2[1]-p1[1]),
        math.hypot(p3[0]-p2[0], p3[1]-p2[1]),
        math.hypot(p0[0]-p3[0], p0[1]-p3[1])
    ]
    top, right, bottom, left = edge_lengths
    
    # 计算对边比例
    opposite_ratios = [top/bottom if bottom > 0 else 0, left/right if right > 0 else 0]
    
    if not (MIN_OPPOSITE_RATIO <= opposite_ratios[0] <= MAX_OPPOSITE_RATIO and 
            MIN_OPPOSITE_RATIO <= opposite_ratios[1] <= MAX_OPPOSITE_RATIO):
        return False, f"对边不等", None
    
    # 计算四个角的角度
    angles = []
    for i in range(4):
        p_prev = pts[i]
        p_curr = pts[(i+1)%4]
        p_next = pts[(i+2)%4]
        v1 = [p_curr[0]-p_prev[0], p_curr[1]-p_prev[1]]
        v2 = [p_next[0]-p_curr[0], p_next[1]-p_curr[1]]
        dot = v1[0]*v2[0] + v1[1]*v2[1]
        det = v1[0]*v2[1] - v1[1]*v2[0]
        angle = abs(math.degrees(math.atan2(det, dot)))
        angles.append(angle)
    
    if not all(MIN_ANGLE <= angle <= MAX_ANGLE for angle in angles):
        return False, f"角度异常", None
    
    # 计算宽高比
    x, y, w, h = cv2.boundingRect(approx)
    aspect_ratio = w / h if h > 0 else 0
    
    rect_info = {
        'aspect_ratio': aspect_ratio,
        'angles': angles,
        'opposite_ratios': opposite_ratios
    }
    
    return True, "规则矩形", rect_info

def draw_rectangle_info(img, rect_info, x_offset=320):
    """在右上角显示矩形检测信息"""
    if rect_info is None:
        cv2.putText(img, "No Rectangle", (x_offset + 5, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
        cv2.putText(img, "Detected", (x_offset + 5, 35), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
        return
    
    # 显示宽高比
    cv2.putText(img, f"Ratio:{rect_info['aspect_ratio']:.2f}", (x_offset + 5, 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 0), 1)
    
    # 显示角度
    angles_text = f"Ang:[{rect_info['angles'][0]:.0f},{rect_info['angles'][1]:.0f},"
    cv2.putText(img, angles_text, (x_offset + 5, 35), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 0), 1)
    angles_text2 = f"{rect_info['angles'][2]:.0f},{rect_info['angles'][3]:.0f}]"
    cv2.putText(img, angles_text2, (x_offset + 5, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 0), 1)
    
    # 显示对边比例
    ratios = rect_info['opposite_ratios']
    cv2.putText(img, f"Edge:[{ratios[0]:.2f},{ratios[1]:.2f}]", (x_offset + 5, 65), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 0), 1)

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    gc.disable()
    print("分屏显示版jiguangcar程序启动...")
    
    # 初始化设备
    disp = display.Display()
    cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
    laser_detector = PurpleLaserDetector()
    
    # 初始化虚拟按键和触摸屏
    buttons = VirtualButtons()
    ts = init_touchscreen()
    current_mode = "center"
    last_touch_pos = (0, 0)
    binary_threshold = BINARY_THRESHOLD
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        uart.set_frame("$$", "##", True)
    else:
        print("串口初始化失败")
        exit()

    # FPS计算初始化
    fps = 0
    last_time = time.ticks_ms()
    frame_count = 0

    while not app.need_exit():
        frame_count += 1
        
        # 处理触摸输入
        current_time = time.time()
        if ts and (current_time - buttons.last_touch_time) > buttons.touch_debounce:
            try:
                if ts.available():
                    touch_data = ts.read()
                    if len(touch_data) >= 3:
                        touch_x, touch_y, pressed = touch_data[0], touch_data[1], touch_data[2]
                        last_touch_pos = (touch_x, touch_y)
                        if pressed:
                            action = buttons.check_touch(touch_x, touch_y)
                            if action:
                                buttons.last_touch_time = current_time
                                if action == "center":
                                    current_mode = "center"
                                    print("切换到中心点模式")
                                elif action == "circle":
                                    current_mode = "circle"
                                    print("切换到圆形模式")
                                elif action == "thresh_up":
                                    binary_threshold = min(255, binary_threshold + 3)
                                    print(f"阈值增加到: {binary_threshold}")
                                elif action == "thresh_down":
                                    binary_threshold = max(1, binary_threshold - 3)
                                    print(f"阈值减少到: {binary_threshold}")
            except Exception as e:
                if frame_count % 120 == 0:
                    print(f"Touch processing error: {e}")
        
        # 计算FPS
        current_time_ms = time.ticks_ms()
        if current_time_ms - last_time > 0:
            fps = 1000.0 / (current_time_ms - last_time)
        last_time = current_time_ms
        
        # 读取图像
        img = cam.read()
        if img is None:
            continue
        img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
        
        # 创建左侧彩色输出图像
        output_left = img_cv.copy()
        
        # 创建右侧二值化图像
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
        binary_bgr = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)  # 转换为3通道

        # 1. 矩形检测（保持原有逻辑）
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        current_rect_info = None
        
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if not (MIN_CONTOUR_AREA < area < MAX_CONTOUR_AREA):
                continue
            
            epsilon = 0.03 * cv2.arcLength(cnt, True)
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            if len(approx) != TARGET_SIDES:
                continue
            
            x, y, w, h = cv2.boundingRect(approx)
            if h == 0:
                continue
            aspect_ratio = w / h
            if not (MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO):
                continue
            
            is_regular, reason, rect_info = is_regular_rectangle(approx)
            if not is_regular:
                continue
            
            quads.append((approx, area))
            current_rect_info = rect_info

        # 只保留面积最大的矩形
        inner_quads = []
        if quads:
            largest_quad = max(quads, key=lambda x: x[1])
            inner_quads = [largest_quad]

        # 2. 处理检测到的矩形
        center_points = []
        all_circle_points = []

        for approx, area in inner_quads:
            pts = approx.reshape(4, 2).astype(np.float32)
            cv2.drawContours(output_left, [approx], -1, (0, 255, 0), 2)

            # 透视变换获取中心点
            M, M_inv, src_pts = perspective_transform(pts, CORRECTED_WIDTH, CORRECTED_HEIGHT)
            if M_inv is not None:
                corrected_center = (CORRECTED_WIDTH//2, CORRECTED_HEIGHT//2)
                center_np = np.array([[corrected_center]], dtype=np.float32)
                original_center = cv2.perspectiveTransform(center_np, M_inv)[0][0]
                cx, cy = int(original_center[0]), int(original_center[1])
                cv2.circle(output_left, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))
            else:
                cx = int(np.mean(pts[:, 0]))
                cy = int(np.mean(pts[:, 1]))
                cv2.circle(output_left, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))

            # 圆形模式处理
            if current_mode == "circle":
                if M_inv is not None:
                    corrected_center = (CORRECTED_WIDTH//2, CORRECTED_HEIGHT//2)
                    corrected_circle = generate_circle_points(corrected_center, CIRCLE_RADIUS, CIRCLE_NUM_POINTS)
                    corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                    original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                    original_points = [(int(x), int(y)) for x, y in original_points]
                    all_circle_points.extend(original_points)
                    for (x, y) in original_points:
                        cv2.circle(output_left, (x, y), 2, (0, 0, 255), -1)
                else:
                    simple_circle = generate_circle_points((cx, cy), 30, CIRCLE_NUM_POINTS)
                    all_circle_points.extend(simple_circle)
                    for (x, y) in simple_circle:
                        cv2.circle(output_left, (x, y), 2, (0, 0, 255), -1)

        # 3. 激光检测
        output_left, laser_points = laser_detector.detect(output_left)

        # 4. 串口发送数据
        if current_mode == "center":
            if center_points:
                cx, cy = center_points[0]
                micu_printf(f"R,{cx},{cy}")
            else:
                micu_printf("R,0,0")
        elif current_mode == "circle":
            if all_circle_points:
                circle_data = f"C,{len(all_circle_points)}"
                for (x, y) in all_circle_points:
                    circle_data += f",{x},{y}"
                micu_printf(circle_data)

        # 5. 绘制目标点标记（仅在左侧）
        target_x, target_y = 150, 95
        cross_size = 5
        cv2.line(output_left, (target_x - cross_size, target_y), (target_x + cross_size, target_y), (255, 0, 255), 2)
        cv2.line(output_left, (target_x, target_y - cross_size), (target_x, target_y + cross_size), (255, 0, 255), 2)
        cv2.putText(output_left, f"({target_x},{target_y})", (target_x + 8, target_y - 8),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)

        # 6. 绘制虚拟按键（仅在左侧）
        buttons.draw_buttons(output_left, current_mode, binary_threshold)

        # 7. 显示统计信息（仅在左侧）
        cv2.putText(output_left, f"FPS: {fps:.1f}", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        mode_text = f"Mode: {current_mode.upper()}"
        cv2.putText(output_left, mode_text, (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 255), 1)
        cv2.putText(output_left, f"Touch: {last_touch_pos}", (10, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 0), 1)

        # 8. 创建分屏显示图像
        split_view = np.hstack([output_left, binary_bgr])
        
        # 9. 在右侧显示矩形检测信息
        draw_rectangle_info(split_view, current_rect_info, 320)
        
        # 在右侧添加二值化标题
        cv2.putText(split_view, "Binary View", (325, 230), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        # 显示分屏图像
        img_show = image.cv2image(split_view, bgr=True, copy=False)
        disp.show(img_show)

    print("程序退出")
